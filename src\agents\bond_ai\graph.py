"""Define a ReAct agent using LangGraph.

This agent follows the ReAct pattern (Reasoning and Acting) to solve tasks
by thinking step by step and using tools when needed.
"""

import functools
import operator
from typing import Annotated, Literal, Sequence, TypedDict
from langchain_openai import ChatOpenAI
from langgraph.graph import StateGraph, END

from langchain_core.messages import AIMessage, HumanMessage, SystemMessage, BaseMessage
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
from pydantic import BaseModel

from bond_ai.configuration import Configuration
from bond_ai.state import AgentState

from bond_ai.nodes import  tool_node, call_model, table_indexing_node, planner_agent

from langgraph.prebuilt import create_react_agent

def should_continue(state: AgentState):
    """Determine whether to continue with tool execution or end the conversation."""
    messages = state["messages"]
    last_message = messages[-1]
    
    # If we're in chat mode, always end (no tools should be used)
    is_chat_mode = state.get("mode") == "chat"
    if is_chat_mode:
        return "end"
    
    # If there are tool calls, continue to the tool node
    if isinstance(last_message, AIMessage) and last_message.tool_calls:
        return "continue"
    
    # Otherwise, end the conversation
    return "end"


#POC
from enum import Enum

class AgentName(Enum):
    # agent_build_prospect_list_from_db = "agent_build_prospect_list_from_db"
    # agent_linkedin = "agent_linkedin"
    # agent_run_columns_or_cells = "agent_run_columns_or_cells"
    # agent_email_and_phone = "agent_email_and_phone"
    # agent_research_column_agent = "agent_research_column_agent"
    # agent_icp_and_persona = "agent_icp_and_persona"
    # agent_message_copy = "agent_message_copy"
    # agent_action_table = "agent_action_table"
    # agent_upload_csv = "agent_upload_csv"
    # agent_http_column = "agent_http_column"
    # agent_webhook_column = "agent_webhook_column"
    # agent_formula = "agent_formula"
    # agent_clean_up_column = "agent_clean_up_column"
    agent_research = "agent_research"
    agent_code = "agent_coder"
    
    
members = [agent.value for agent in AgentName]
# members =["agent_build_prospect_list_from_db"]

#members = ["Researcher", "Coder"]    
class MyAgentstate(TypedDict):
    messages: Annotated[Sequence[BaseMessage],operator.add]
     # the next node to run
    next: str
    
def agent_node(state, agent, name):
    result = agent.invoke(state)
    return {
        "messages": [HumanMessage(content=result["messages"][-1].content, name=name)]
    }


system_prompt = (
    "You are a supervisor tasked with managing a conversation between the"
    " following workers: {members}. Given the following user request,"
    " respond with the worker to act next. Each worker will perform a"
    " task and respond with their results and status. When finished," 
    " respond with FINISH." )

options = ["FINISH"] + members

class routeResponse(BaseModel):
    next: Literal[*options] # type: ignore

prompt = ChatPromptTemplate.from_messages(
    [
        ("system", system_prompt),
        MessagesPlaceholder(variable_name="messages"),
        (
            "system",
            "Given the conversation above, who should act next?"
            "Or should we FINISH? Select one of {options}"
        ),
    ]
).partial(options=str(options), members=", ".join(members))

llm = ChatOpenAI(model="gpt-4o-mini")

def supervisor_agent(state):
    supervisor_chain = prompt | llm.with_structured_output(routeResponse)
    return supervisor_chain.invoke(state)    


def _create_react_agent(name: str, system_prompt: str, tools: list, llm=None):
    """Factory function to create a ReAct agent with system prompt and tools."""
    if llm is None:
        llm = ChatOpenAI(model="gpt-4o-mini")
    
    prompt = ChatPromptTemplate.from_messages([
        ("system", system_prompt),
        MessagesPlaceholder(variable_name="messages")
    ])
    
    agent = create_react_agent(llm, tools=tools, state_modifier=prompt)
    return functools.partial(agent_node, agent=agent, name=name)

from langchain_community.tools.tavily_search import TavilySearchResults
tavily_tool = TavilySearchResults(max_results=5)

from langchain_experimental.tools import PythonREPLTool
python_repl_tool = PythonREPLTool()

# Define agent configurations
AGENT_CONFIGS = {
    "Researcher": {
        "system_prompt": "You are a research expert. Find accurate, relevant information and cite sources.",
        "tools": [tavily_tool]
    },
    "Coder": {
        "system_prompt": "You are a Python coding expert. Write clean, efficient code and explain your approach.", 
        "tools": [python_repl_tool]
    }
}

workflow = StateGraph(MyAgentstate)
# Create all agents at once
for name, config in AGENT_CONFIGS.items():
    node = _create_react_agent(name=name, **config)
    workflow.add_node(name, node)
    
workflow.add_node("supervisor", supervisor_agent)
for member in members:
    # We want our workers to ALWAYS "report back" to the supervisor when done
    workflow.add_edge(member, "supervisor")
    # The supervisor populates the "next" field in the graph state # which routes to a node or finishes 


conditional_map = {k: k for k in members}
conditional_map["FINISH"] = END
workflow.add_conditional_edges("supervisor", lambda x: x["next"], conditional_map)

workflow.set_entry_point("supervisor")

graph = workflow.compile()


# Configuration flag to control graph behavior
TEST_MODE = True  
# Define the graph
def create_graph():
    """Create and return the ReAct agent graph."""
 
 
    workflow = StateGraph(AgentState, config_schema=Configuration)
    workflow.add_node("table_indexing", table_indexing_node)
    workflow.set_entry_point("table_indexing")
    workflow.add_node("planner", planner_agent)
    workflow.add_edge("table_indexing", "planner")


    if TEST_MODE:
        workflow.add_edge("planner", END)    
        graph = workflow.compile()
    else:
        
        workflow.add_node("agent", call_model)
        workflow.add_node("tools", tool_node)
        workflow.add_edge("planner", "agent")
        workflow.add_conditional_edges(
            "agent",
            should_continue,
            {
                "continue": "tools",
                "end": END,
            },
        )
        workflow.add_edge("tools", "agent")  
        graph = workflow.compile()
    
    graph.name = "Bond AI"  # Custom name for LangSmith
        
    return graph


# Create the graph
#graph = create_graph()
