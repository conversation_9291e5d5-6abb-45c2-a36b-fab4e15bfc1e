
import json



from langchain_core.runnables import RunnableConfig

from bond_ai.configuration import Configuration
from bond_ai.state import Agent<PERSON>tate


from src.outbond_ai_assistant_v2.tools.read_table_tools.read_table import read_table_data_tool
from src.outbond_ai_assistant_v2.supabase.read_table.models import TableDataRequest
from src.outbond_ai_assistant_v2.supabase.table.update_columns import update_column_by_id
from src.outbond_ai_assistant_v2.supabase.table.get_columns import get_columns_by_table_id




# def get_summary_by_table_id(table_id: str):
#     """Node that indexes table data and creates a summary for the agent."""
   

#     def update_column_description(column_id: str, description: str) -> bool:
#         """Helper to update column description in database."""
#         success, _ = update_column_by_id(config=config, column_id=int(column_id), fields={"agent_description": description})
#         return success is not None
    
#     try:
#         # Get columns data
#         columns_data, error = get_columns_by_table_id(configuration.table_id)
#         if error:
#             raise Exception(error)
        
#         # Handle case when there are no columns
#         if not columns_data:
#             return {"table_summary": "There is no columns in the table yet"}
        
#         # Find columns needing descriptions (only those without summaries)
#         columns_needing_descriptions = [
#             col for col in columns_data 
#             if not col.get('agent_description') or not str(col.get('agent_description', '')).strip()
#         ]
        
#         # Process only columns that need descriptions
#         if columns_needing_descriptions:
#             column_names = [col['name'] for col in columns_data]
            
#             # Get structured analysis with summaries
#             request = TableDataRequest(
#                 table_id="", max_rows=10, column_names=column_names, 
#                 row=False, apply_table_filters=True, apply_table_sorts=True, include_summary=True
#             )
#             response = read_table_data_tool.invoke({"request": request}, config)
            
#             if not response.success or not response.columns:
#                 raise Exception(f"Failed to analyze table data: {response.error_message or 'No column data returned'}")
            
#             # Create name-to-id mapping
#             name_to_id = {col['name']: col['id'] for col in columns_data}
            
#             # Process each column from response
#             for idx, column_summary in enumerate(response.columns):
#                 summary_dict = column_summary.model_dump() if hasattr(column_summary, 'model_dump') else column_summary
                
#                 # Find column name (exclude standard keys)
#                 standard_keys = {'is_runnable', 'column_run_status', 'column_data_summary'}
#                 column_name = next((key for key in summary_dict.keys() if key not in standard_keys), 
#                                  column_names[idx] if idx < len(column_names) else None)
                
#                 column_id = name_to_id.get(column_name) if column_name else None
                
#                 if not column_id:
#                     raise Exception(f"Could not match column '{column_name}' to database ID")
                
#                 # Extract only the summary from the column data
#                 summary_text = summary_dict.get('column_data_summary', 'Summary not available')
                
#                 if not update_column_description(column_id, summary_text):
#                     raise Exception(f"Failed to update description for column '{column_name}' (ID: {column_id})")
                
#                 # Update local data with just the summary
#                 for col in columns_data:
#                     if col['id'] == column_id:
#                         col['agent_description'] = summary_text
#                         break
        
#         # Always fetch fresh schema data for the final table summary
#         column_names = [col['name'] for col in columns_data]
#         request = TableDataRequest(
#             table_id="", max_rows=10, column_names=column_names, 
#             row=False, apply_table_filters=True, apply_table_sorts=True, include_summary=False
#         )
#         fresh_response = read_table_data_tool.invoke({"request": request}, config)
        
#         if not fresh_response.success or not fresh_response.columns:
#             raise Exception(f"Failed to get fresh table data: {fresh_response.error_message or 'No column data returned'}")
        
#         # Create comprehensive table summary combining fresh schema + stored summaries
#         table_summary_parts = []
#         name_to_description = {col['name']: col.get('agent_description', '') for col in columns_data}
#         name_to_id = {col['name']: col['id'] for col in columns_data}
        
#         for idx, column_schema in enumerate(fresh_response.columns):
#             schema_dict = column_schema.model_dump() if hasattr(column_schema, 'model_dump') else column_schema
            
#             # Find column name (exclude standard keys)
#             standard_keys = {'is_runnable', 'column_run_status', 'column_data_summary'}
#             column_name = next((key for key in schema_dict.keys() if key not in standard_keys), 
#                              column_names[idx] if idx < len(column_names) else None)
            
#             if column_name:
#                 # Combine fresh schema with stored summary and column ID
#                 summary_data = {
#                     'column_name': column_name,
#                     'column_id': name_to_id.get(column_name),
#                     'data_summary': name_to_description.get(column_name, 'No summary available'),
#                     **schema_dict
#                 }
                
#                 table_summary_parts.append(json.dumps(summary_data, indent=2, default=str))
        
#         # Ensure all columns have descriptions
#         for col in columns_data:
#             if not col.get('agent_description'):
#                 raise Exception(f"Column '{col['name']}' (ID: {col['id']}) has no description after processing")
        
#         return {"table_summary": "\n\n".join(table_summary_parts)}
        
#     except Exception as e:
#         raise Exception(f"Table indexing failed: {str(e)}")




def table_indexing_node(state: AgentState, config: RunnableConfig):
    """Node that indexes table data and creates a summary for the agent."""
    configuration = Configuration.from_runnable_config(config)
    print("table_indexing_node configuration", configuration)

    def update_column_description(column_id: str, description: str) -> bool:
        """Helper to update column description in database."""
        success, _ = update_column_by_id(config=config, column_id=int(column_id), fields={"agent_description": description})
        return success is not None
    
    try:
        # Get columns data
        columns_data, error = get_columns_by_table_id(configuration.table_id)
        if error:
            raise Exception(error)
        
        # Handle case when there are no columns
        if not columns_data:
            return {"table_summary": "There is no columns in the table yet"}
        
        # Find columns needing descriptions (only those without summaries)
        columns_needing_descriptions = [
            col for col in columns_data 
            if not col.get('agent_description') or not str(col.get('agent_description', '')).strip()
        ]
        
        # Process only columns that need descriptions
        if columns_needing_descriptions:
            column_names = [col['name'] for col in columns_data]
            
            # Get structured analysis with summaries
            request = TableDataRequest(
                table_id="", max_rows=10, column_names=column_names, 
                row=False, apply_table_filters=True, apply_table_sorts=True, include_summary=True
            )
            response = read_table_data_tool.invoke({"request": request}, config)
            
            if not response.success or not response.columns:
                raise Exception(f"Failed to analyze table data: {response.error_message or 'No column data returned'}")
            
            # Create name-to-id mapping
            name_to_id = {col['name']: col['id'] for col in columns_data}
            
            # Process each column from response
            for idx, column_summary in enumerate(response.columns):
                summary_dict = column_summary.model_dump() if hasattr(column_summary, 'model_dump') else column_summary
                
                # Find column name (exclude standard keys)
                standard_keys = {'is_runnable', 'column_run_status', 'column_data_summary'}
                column_name = next((key for key in summary_dict.keys() if key not in standard_keys), 
                                 column_names[idx] if idx < len(column_names) else None)
                
                column_id = name_to_id.get(column_name) if column_name else None
                
                if not column_id:
                    raise Exception(f"Could not match column '{column_name}' to database ID")
                
                # Extract only the summary from the column data
                summary_text = summary_dict.get('column_data_summary', 'Summary not available')
                
                if not update_column_description(column_id, summary_text):
                    raise Exception(f"Failed to update description for column '{column_name}' (ID: {column_id})")
                
                # Update local data with just the summary
                for col in columns_data:
                    if col['id'] == column_id:
                        col['agent_description'] = summary_text
                        break
        
        # Always fetch fresh schema data for the final table summary
        column_names = [col['name'] for col in columns_data]
        request = TableDataRequest(
            table_id="", max_rows=10, column_names=column_names, 
            row=False, apply_table_filters=True, apply_table_sorts=True, include_summary=False
        )
        fresh_response = read_table_data_tool.invoke({"request": request}, config)
        
        if not fresh_response.success or not fresh_response.columns:
            raise Exception(f"Failed to get fresh table data: {fresh_response.error_message or 'No column data returned'}")
        
        # Create comprehensive table summary combining fresh schema + stored summaries
        table_summary_parts = []
        name_to_description = {col['name']: col.get('agent_description', '') for col in columns_data}
        name_to_id = {col['name']: col['id'] for col in columns_data}
        
        for idx, column_schema in enumerate(fresh_response.columns):
            schema_dict = column_schema.model_dump() if hasattr(column_schema, 'model_dump') else column_schema
            
            # Find column name (exclude standard keys)
            standard_keys = {'is_runnable', 'column_run_status', 'column_data_summary'}
            column_name = next((key for key in schema_dict.keys() if key not in standard_keys), 
                             column_names[idx] if idx < len(column_names) else None)
            
            if column_name:
                # Combine fresh schema with stored summary and column ID
                summary_data = {
                    'column_name': column_name,
                    'column_id': name_to_id.get(column_name),
                    'data_summary': name_to_description.get(column_name, 'No summary available'),
                    **schema_dict
                }
                
                table_summary_parts.append(json.dumps(summary_data, indent=2, default=str))
        
        # Ensure all columns have descriptions
        for col in columns_data:
            if not col.get('agent_description'):
                raise Exception(f"Column '{col['name']}' (ID: {col['id']}) has no description after processing")
        
        return {"table_summary": "\n\n".join(table_summary_parts)}
        
    except Exception as e:
        raise Exception(f"Table indexing failed: {str(e)}")
