"""Define a ReAct agent using LangGraph.

This agent follows the ReAct pattern (Reasoning and Acting) to solve tasks
by thinking step by step and using tools when needed.
"""

import functools
import operator
from typing import Annotated, Literal, Sequence, TypedDict
from langchain_openai import ChatOpenAI
from langgraph.graph import StateGraph, END

from langchain_core.messages import AIMessage, HumanMessage, SystemMessage, BaseMessage
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
from pydantic import BaseModel

from bond_ai.configuration import Configuration
from bond_ai.state import  BondAIAgentState



from langgraph.prebuilt import create_react_agent


# ============================================================================
# SIMPLE AGENT REGISTRY
# ============================================================================

class AgentRegistry:
    """Simple registry for managing agent configurations."""

    def __init__(self):
        self.agents = {}
        self.tools = {}

    def register_agent(self, name: str, system_prompt: str, tools: list, enabled: bool = True, **kwargs):
        """Register an agent with its configuration."""
        self.agents[name] = {
            "system_prompt": system_prompt,
            "tools": tools,
            "enabled": enabled,
            **kwargs
        }
        return self

    def register_tool(self, name: str, tool):
        """Register a tool by name."""
        self.tools[name] = tool
        return self

    def get_enabled_agents(self):
        """Get all enabled agent configurations."""
        return {name: config for name, config in self.agents.items() if config.get("enabled", True)}

    def get_agent_names(self):
        """Get list of enabled agent names."""
        return list(self.get_enabled_agents().keys())

    def enable_agent(self, name: str):
        """Enable an agent."""
        if name in self.agents:
            self.agents[name]["enabled"] = True

    def disable_agent(self, name: str):
        """Disable an agent."""
        if name in self.agents:
            self.agents[name]["enabled"] = False

# Create global registry
agent_registry = AgentRegistry()

# Import and register tools
from langchain_community.tools.tavily_search import TavilySearchResults
from langchain_experimental.tools import PythonREPLTool

tavily_tool = TavilySearchResults(max_results=5)
python_repl_tool = PythonREPLTool()

agent_registry.register_tool("tavily_search", tavily_tool)
agent_registry.register_tool("python_repl", python_repl_tool)

# Register agents using the simple registry
agent_registry.register_agent(
    "Researcher",
    "You are a research expert. Find accurate, relevant information and cite sources.",
    [tavily_tool]
).register_agent(
    "Coder",
    "You are a Python coding expert. Write clean, efficient code and explain your approach.",
    [python_repl_tool]
)

# Get members from registry for backward compatibility
members = agent_registry.get_agent_names()
 

    
def agent_node(state, agent, name):
    result = agent.invoke(state)
    return {
        "messages": [HumanMessage(content=result["messages"][-1].content, name=name)]
    }


system_prompt = (
    "You are a supervisor tasked with managing a conversation between the"
    " following workers: {members}. Given the following user request,"
    " respond with the worker to act next. Each worker will perform a"
    " task and respond with their results and status. When finished," 
    " respond with FINISH." )

options = ["FINISH"] + members

class routeResponse(BaseModel):
    next: Literal[*options] # type: ignore

prompt = ChatPromptTemplate.from_messages(
    [
        ("system", system_prompt),
        MessagesPlaceholder(variable_name="messages"),
        (
            "system",
            "Given the conversation above, who should act next?"
            "Or should we FINISH? Select one of {options}"
        ),
    ]
).partial(options=str(options), members=", ".join(members))

llm = ChatOpenAI(model="gpt-4o-mini")

def supervisor_agent(state):
    supervisor_chain = prompt | llm.with_structured_output(routeResponse)
    return supervisor_chain.invoke(state)    


def _create_react_agent(name: str, system_prompt: str, tools: list, llm=None):
    """Factory function to create a ReAct agent with system prompt and tools."""
    if llm is None:
        llm = ChatOpenAI(model="gpt-4o-mini")

    prompt = ChatPromptTemplate.from_messages([
        ("system", system_prompt),
        MessagesPlaceholder(variable_name="messages")
    ])

    agent = create_react_agent(llm, tools=tools, state_modifier=prompt)
    return functools.partial(agent_node, agent=agent, name=name)


def create_workflow_from_registry():
    """Create workflow using the agent registry."""
    workflow = StateGraph(BondAIAgentState)

    # Create all enabled agents from registry using existing pattern
    enabled_agents = agent_registry.get_enabled_agents()
    for name, config in enabled_agents.items():
        node = _create_react_agent(name=name, **config)
        workflow.add_node(name, node)
        print(f"✓ Added agent: {name}")

    # Add supervisor
    workflow.add_node("supervisor", supervisor_agent)

    # Connect agents to supervisor
    current_members = agent_registry.get_agent_names()
    for member in current_members:
        workflow.add_edge(member, "supervisor")

    # Create conditional routing
    conditional_map = {k: k for k in current_members}
    conditional_map["FINISH"] = END
    workflow.add_conditional_edges("supervisor", lambda x: x["next"], conditional_map)

    workflow.set_entry_point("supervisor")
    return workflow.compile()


# ============================================================================
# UTILITY FUNCTIONS FOR EASY AGENT MANAGEMENT
# ============================================================================

def add_agent(name: str, system_prompt: str, tools: list, enabled: bool = True):
    """Add a new agent to the registry."""
    agent_registry.register_agent(name, system_prompt, tools, enabled)
    print(f"✓ Added agent: {name}")

def enable_agent(name: str):
    """Enable an agent."""
    agent_registry.enable_agent(name)
    print(f"✓ Enabled agent: {name}")

def disable_agent(name: str):
    """Disable an agent."""
    agent_registry.disable_agent(name)
    print(f"✓ Disabled agent: {name}")

def list_agents():
    """List all agents and their status."""
    print("\n=== AGENT REGISTRY ===")
    for name, config in agent_registry.agents.items():
        status = "✓ ENABLED" if config.get("enabled", True) else "✗ DISABLED"
        tools_count = len(config.get("tools", []))
        print(f"{status} | {name} | Tools: {tools_count}")
    print()

def rebuild_workflow():
    """Rebuild the workflow with current registry state."""
    global graph, members
    members = agent_registry.get_agent_names()
    graph = create_workflow_from_registry()
    print("✓ Workflow rebuilt")
    return graph

# Create the initial workflow
graph = create_workflow_from_registry()


# ============================================================================
# EXAMPLE USAGE OF THE FLEXIBLE AGENT REGISTRY
# ============================================================================

"""
Example of how to use the flexible agent registry:

# Add a new agent
add_agent(
    "DataAnalyst",
    "You are a data analysis expert. Analyze data and provide insights.",
    [python_repl_tool, tavily_tool]
)

# Disable an existing agent
disable_agent("Coder")

# List all agents
list_agents()

# Rebuild workflow with new configuration
rebuild_workflow()

# Add a custom tool and agent
from langchain_core.tools import tool

@tool
def custom_calculator(expression: str) -> str:
    \"\"\"Calculate mathematical expressions.\"\"\"
    try:
        result = eval(expression)
        return f"Result: {result}"
    except:
        return "Error in calculation"

agent_registry.register_tool("calculator", custom_calculator)

add_agent(
    "MathExpert",
    "You are a mathematics expert. Solve mathematical problems step by step.",
    [custom_calculator]
)

rebuild_workflow()
"""
